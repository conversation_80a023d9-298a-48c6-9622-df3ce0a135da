"use client";

import { useState, useEffect } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";
import { ApiFieldsResponse, UpdateFieldsRequest } from "@/types/project-api";

export function useProjectFields(projectId: string | number, phaseId: number | null) {
  const [fieldsData, setFieldsData] = useState<ApiFieldsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchFields = async () => {
    if (!phaseId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(
        `${API_ROUTES.PROJECT_FIELDS}${projectId}/phases/${phaseId}/fields/`
      );

      if (response.status === 200) {
        setFieldsData(response.data);
      } else {
        setError("Failed to fetch fields data");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch fields data");
    } finally {
      setLoading(false);
    }
  };

  const updateFields = async (fieldsToUpdate: UpdateFieldsRequest) => {
    if (!phaseId) {
      throw new Error("Phase ID is required for updating fields");
    }

    try {
      setUpdating(true);
      setError(null);

      const response = await axiosInstance.post(
        `${API_ROUTES.UPDATE_PROJECT_FIELDS}${projectId}/phases/${phaseId}/fields/update/`,
        fieldsToUpdate
      );

      if (response.status === 200) {
        // Refetch fields data after successful update
        await fetchFields();
        return response.data;
      } else {
        throw new Error("Failed to update fields");
      }
    } catch (err: any) {
      setError(err.message || "Failed to update fields");
      throw err;
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    if (projectId && phaseId) {
      fetchFields();
    }
  }, [projectId, phaseId]);

  return {
    fieldsData,
    loading,
    updating,
    error,
    fetchFields,
    updateFields,
  };
}
